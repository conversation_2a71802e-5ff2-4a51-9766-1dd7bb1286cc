<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": ""
  }
}
</route>

<script lang="ts" setup>
const uploader = ref()
const fileList = ref<any[]>([])
function handleChange(fileList: any[]) {
  console.log(fileList)
}
function onUploadClick() {
  console.log('开始上传')
  uploader.value?.submit()
}
defineExpose({
  onUploadClick,
})
</script>

<template>
  <view class="">
    <wd-upload ref="uploader" multiple :file-list="fileList" action="/" @change="(value: any) => handleChange(value.fileList)">
      <template #preview-cover="{ file, index }">
        <view class="preview-cover">
          {{ file?.name || `文件${index + 1}` }}
        </view>
      </template>
    </wd-upload>
  </view>
</template>

<style lang="scss" scoped>
//
</style>

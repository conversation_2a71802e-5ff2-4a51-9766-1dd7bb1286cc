<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "待办"
  }
}
</route>

<script lang="ts" setup>
import type { ProjectStatus } from '@/api/business'
import { projectInfo } from '@/api/business'

defineOptions({
  name: 'Todo',
})

interface TodoItemList {
  name: string
  status?: string
}

interface TodoListItem {
  name: string
  color: string
  color50Opacity: string
  color5Opacity: string
  list: TodoItemList[]
}

interface TodoList extends Array<TodoListItem> {}

// 有数据时的完整数据，可以取消注释来测试有数据的状态
const todoList = ref<TodoList>([
  {
    name: '图片待提交',
    color: 'rgba(0, 133, 208, 1)',
    color50Opacity: 'rgba(0, 133, 208, 0.5)',
    color5Opacity: 'rgba(0, 133, 208, 0.05)',
    list: [],
  },
  {
    name: '用户申诉',
    color: 'rgb(214, 146, 81)',
    color50Opacity: 'rgba(214, 146, 81, 0.5)',
    color5Opacity: 'rgba(214, 146, 81, 0.05)',
    list: [],
  },
  {
    name: '待阅信息',
    color: '#3DCCCC',
    color50Opacity: '#3DCCCC80',
    color5Opacity: '#3DCCCC0D',
    list: [],
  },
])

function handleHeaderClick(list: TodoListItem) {
  uni.navigateTo({
    url: `/pages/list/index?type=${list.name}`,
  })
}

function handleListClick(todo: TodoListItem, list: TodoItemList) {
  uni.navigateTo({
    url: `/pages/list/index?name=${list.name}&todoName=${todo.name}`,
  })
}

function handleStatusClick(todo: TodoListItem, list: TodoItemList) {
  uni.navigateTo({
    url: `/pages/list/index?name=${list.name}&todoName=${todo.name}`,
  })
}

async function getProjectInfo(type: ProjectStatus) {
  const res = await projectInfo({
    pageNum: 1,
    pageSize: 3,
    projectStatus: type,
  })
  return res?.result?.list || []
}

async function init() {
  const res1 = await getProjectInfo('图像待提交')
  const res2 = await getProjectInfo('申诉待审批')
  const res3 = await getProjectInfo('已结束')
  todoList.value[0].list = res1
  todoList.value[1].list = res2
  todoList.value[2].list = res3
}

onShow(() => {
  init()
})
</script>

<template>
  <!-- TODO 布局溢出，带滚动条 -->
  <view class="global-bg-image-bg" min-h-screen flex flex-col items-center pt-20rpx>
    <wd-card
      v-for="todo in todoList"
      :key="todo.name"
      custom-class="w-650rpx"
      custom-content-class="text-black"
      border-y="3px solid rd-5px"
      :style="{ borderColor: todo.color }"
    >
      <template #title>
        <view
          flex items-center justify-between
          p-b-10px
          border-b="1px solid rd-5px"
          :style="{ borderColor: todo.color }"
          @click="handleHeaderClick(todo)"
        >
          <text>
            {{ todo.name }}
          </text>
          <text text-sm text-gray>
            更多&gt;&gt;
          </text>
        </view>
      </template>

      <!-- 当该分类有数据时显示列表 -->
      <template v-if="todo.list.length > 0">
        <view
          v-for="(list, index) in todo.list"
          :key="list.name + index"
          border-b="1px solid"
          my-10px flex items-center justify-between border-rd-5px px-3px py-5px first:mt-0
          :style="{ backgroundColor: todo.color5Opacity, borderColor: todo.color50Opacity }"
          @click="handleListClick(todo, list)"
        >
          <text>
            {{ list.name }}
          </text>
          <view v-if="list.status" @click="handleStatusClick(todo, list)">
            <wd-tag type="warning" plain>
              {{ list.status }}
            </wd-tag>
          </view>
        </view>
      </template>

      <!-- 当该分类无数据时显示空状态提示 -->
      <template v-else>
        <view class="empty-content" flex flex-col items-center justify-center p-y-40rpx>
          <wd-status-tip
            image="content"
            tip="暂无数据"
            :image-size="{ width: 120, height: 90 }"
          />
        </view>
      </template>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
//
</style>

<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "数据看板"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import { projectInfo } from '@/api/business'

const source = ref([
  {
    label: '识别任务总量:',
    value: '1000000',
  },
  {
    label: '识别准确率:',
    value: '1000000',
  },
  {
    label: '审核通过率:',
    value: '1000000',
  },
  {
    label: '审核驳回率:',
    value: '1000000',
  },
  {
    label: '申诉数量:',
    value: '1000000',
  },
])
const fastSource = ref([
  {
    image: '/static/images/xinzhen.png',
    label: '新增项目',
  },
  {
    image: '/static/images/shensu.png',
    label: '申诉项目',
  },
])
const list = ref<string[]>(['项目清单', 'AI识别结果', '项目结束'])

const current = ref('项目清单')

const detailsList = ref([])

function getProjectInfo(type: ProjectStatus) {
  detailsList.value = []
  projectInfo({
    pageNum: 1,
    pageSize: 10,
    projectStatus: type,
  }).then((res) => {
    console.log(res, 'res')
    detailsList.value = res.result.list
    console.log(detailsList.value, 'detailsList')
  })
}

function handleSegmented(val: { value: string }) {
  console.log(val.value, 'val')

  if (val.value === '项目清单') {
    getProjectInfo('图像待提交')
  }
  else if (val.value === 'AI识别结果') {
    getProjectInfo('AI识别待确认')
  }
  else {
    getProjectInfo('已结束')
  }
}

function handleAdd(type: string) {
  if (type === '新增项目') {
    uni.navigateTo({
      url: `/pages/newproject/index`,
    })
  }
}
onShow(() => {
  getProjectInfo('图像待提交')
})
</script>

<template>
  <view class="global-bg-image-bg" min-h-screen flex flex-col items-center pt-20rpx>
    <wd-card type="rectangle" border-y="6rpx solid #0085D0" w-700rpx>
      <template #title>
        <view border-b="2rpx solid #0085D0" flex items-center justify-between py-4rpx>
          <text text-xl>数据看板</text>
          <text text-sm text-gray-400>更多 >></text>
        </view>
      </template>
      <view flex flex-wrap justify-between gap-8rpx py-6rpx>
        <view v-for="(item, index) in source" :key="index" w="48%" flex items-center justify-between>
          <view flex items-center py-4rpx>
            <image src="/static/images/shuju.png" mr-6rpx h-40rpx w-40rpx />
            <text text-xs>{{ item.label }}</text>
          </view>
          <text text-xs>{{ item.value }}</text>
        </view>
      </view>
    </wd-card>

    <wd-card type="rectangle" border-y="6rpx solid #0085D0" w-700rpx>
      <template #title>
        <view border-b="2rpx solid #0085D0" flex items-center justify-between py-4rpx>
          <text text-xl>快捷方式</text>
        </view>
      </template>
      <view w-260rpx flex flex-wrap justify-between>
        <view v-for="(item, index) in fastSource" :key="index" flex flex-col items-center @click="handleAdd(item.label)">
          <image :src="item.image" h-120rpx w-120rpx />
          <text text-xs>{{ item.label }}</text>
        </view>
      </view>
    </wd-card>

    <wd-card type="rectangle" border-y="6rpx solid #0085D0" w-700rpx class="car-bg">
      <wd-segmented v-model:value="current" :options="list" border-b="2rpx solid #0085D0" @change="handleSegmented" />

      <view v-for="(item, index) in detailsList" :key="index" mt-20rpx bg-white p-10rpx>
        <view bg-sky-100 p-10rpx text-base>
          {{ item.roomName }}
        </view>
        <view pt-10rpx c-gray-600>
          编号：{{ item.roomCode }}
        </view>
        <view pt-10rpx c-gray-600>
          机房地址：{{ item.roomAddress }}
        </view>
        <view pt-10rpx c-gray-600>
          机房类型：{{ item.roomType }}
        </view>
        <view pt-10rpx c-gray-600>
          任务状态：{{ item.projectStatus }}
        </view>
      </view>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
:deep(.wd-card__title-content),
:deep(.wd-card) {
  padding: 0;
}
:deep(.wd-card) {
  padding: 0 12rpx;
}
:deep(.wd-card.is-rectangle) {
  border-radius: 12rpx;
}
:deep(.wd-card.is-rectangle .wd-card__content) {
  padding: 8rpx 0;
}
:deep(.wd-segmented__item.is-active) {
  background: #0085d026;
  color: #0085d0;
}
:deep(.wd-segmented) {
  background-color: #fff;
  border-radius: 0rpx;
}
:deep(.car-bg.wd-card) {
  background-color: #ffffff80;
}
</style>

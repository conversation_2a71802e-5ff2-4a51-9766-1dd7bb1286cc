<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "项目管理"
  }
}
</route>

<script lang="ts" setup>
import type { TagType } from 'wot-design-uni/components/wd-tag/types'

defineOptions({
  name: 'List',
})

// 项目数据接口定义
interface ProjectItem {
  id: string
  name: string
  code: string
  address: string
  type: string
  status: string
}

// 测试无数据状态时，使用空数组
// const projectList = ref<ProjectItem[]>([])

// 有数据时的完整数据，可以取消注释来测试有数据的状态
const projectList = ref<ProjectItem[]>([
  {
    id: '1',
    name: '机房名称1111',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '生产楼',
    status: '图像待提交',
  },
  {
    id: '2',
    name: '机房名称2222',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '生产楼',
    status: '图像待提交',
  },
  {
    id: '3',
    name: '机房名称3333',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '汇聚机房',
    status: '图像待提交',
  },
  {
    id: '4',
    name: '机房名称4444',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '生产楼',
    status: '图像待提交',
  },
])

// 处理查询条件点击
function handleQueryClick() {
  uni.showToast({
    title: '填写查询条件',
    icon: 'none',
  })
}

// 处理项目新增点击
function handleAddProject() {
  uni.showToast({
    title: '项目新增',
    icon: 'none',
  })
}

// 处理项目项点击
function handleProjectClick(project: ProjectItem) {
  uni.navigateTo({
    url: `/pages/detail/index?id=${project.id}`,
  })
}

// 获取机房类型标签样式
function getTypeTagType(type: string): TagType {
  return type === '汇聚机房' ? 'primary' : 'default'
}

// 获取任务状态标签样式
function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case '待审核':
      return 'primary'
    case '已完成':
      return 'success'
    default:
      return 'warning'
  }
}
</script>

<template>
  <view min-h-screen>
    <!-- 头部区域 -->
    <view border-rd="0 0 20rpx 20rpx" m-b-20rpx bg-white bg-opacity-90 p-x-30rpx p-t-20rpx>
      <!-- 项目管理标题和统计 -->
      <view m-b-20rpx flex items-center justify-between>
        <text flex-1 text-32rpx font-bold color="#303133">
          项目管理(共{{ projectList.length }}条)
        </text>
        <wd-button
          type="primary"
          size="small"
          :round="false"
          @click="handleQueryClick()"
        >
          填写查询条件
        </wd-button>
      </view>

      <!-- 项目新增按钮 -->
      <view m-b-30rpx>
        <wd-button
          type="success"
          size="small"
          :round="false"
          @click="handleAddProject"
        >
          项目新增
        </wd-button>
      </view>
    </view>

    <!-- 项目列表 -->
    <view>
      <!-- 当有项目数据时显示列表 -->
      <template v-if="projectList.length > 0">
        <wd-card
          v-for="project in projectList"
          :key="project.id"
          style="margin-bottom: 30rpx; border-radius: 16rpx; overflow: hidden; box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);"
          custom-content-style="padding: 0;"
          @click="handleProjectClick(project)"
        >
          <!-- 机房名称标题 -->
          <template #title>
            <view bg="#E3F2FD" border-rd-8rpx p-x-20rpx p-y-15rpx style="margin: -20rpx -20rpx 0 -20rpx;">
              <text text-32rpx font-bold color="#1976D2">
                {{ project.name }}
              </text>
            </view>
          </template>

          <!-- 项目详情内容 -->
          <view p-20rpx>
            <!-- 编号 -->
            <view m-b-20rpx flex items-center>
              <text min-w-160rpx flex-shrink-0 text-28rpx color="#666">编号：</text>
              <text flex-1 text-28rpx color="#303133">{{ project.code }}</text>
            </view>

            <!-- 机房地址 -->
            <view m-b-20rpx flex items-center>
              <text min-w-160rpx flex-shrink-0 text-28rpx color="#666">机房地址：</text>
              <text flex-1 text-28rpx color="#303133">{{ project.address }}</text>
            </view>

            <!-- 机房类型 -->
            <view m-b-20rpx flex items-center>
              <text min-w-160rpx flex-shrink-0 text-28rpx color="#666">机房类型：</text>
              <wd-tag
                :type="getTypeTagType(project.type)"
                size="small"
              >
                {{ project.type }}
              </wd-tag>
            </view>

            <!-- 任务状态 -->
            <view flex items-center>
              <text min-w-160rpx flex-shrink-0 text-28rpx color="#666">任务状态：</text>
              <wd-tag
                :type="getStatusTagType(project.status)"
                size="small"
              >
                {{ project.status }}
              </wd-tag>
            </view>
          </view>
        </wd-card>
      </template>

      <!-- 当无项目数据时显示空状态提示 -->
      <template v-else>
        <view min-h-400px flex flex-col items-center justify-center p-x-30rpx>
          <wd-status-tip
            image="content"
            tip="暂无项目数据"
            :image-size="{ width: 200, height: 150 }"
          />
        </view>
      </template>
    </view>
  </view>
</template>

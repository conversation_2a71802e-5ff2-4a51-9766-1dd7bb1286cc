<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "工单详情"
  }
}
</route>

<script lang="ts" setup>
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'
import UploadItem from '@/components/UploadItem/UploadItem.vue'

defineOptions({
  name: 'Detail',
})

const uploadItemRef = ref<InstanceType<typeof UploadItem> | null>(null)

// 项目详情数据
interface ProjectDetail {
  name: string
  code: string
  address: string
  type: string
  stage: string
  frameName: string
  executionCity: string
  constructionUnit: string
  districtResponsible: string
  cityResponsible: string
  supervisionUnit: string
  supervisor: string
  constructionLeader: string
  completionTime: string
  taskStatus: string
  creator: string
  createTime: string
}

const projectDetail = ref<ProjectDetail>({
  name: 'XXXXXXXXXXXX',
  code: 'XXXXXXXXXXXXXXXX',
  address: 'XXXXXXXXXXXXX',
  type: '生产楼',
  stage: 'XXXX',
  frameName: 'XXXXXXXXXXXXXXXXX',
  executionCity: 'XXXXXXXXXXXXX',
  constructionUnit: 'XXXXXXXXXXXXXXXXX',
  districtResponsible: 'XXX',
  cityResponsible: 'XXX',
  supervisionUnit: 'XXXXXXXXXXXXX',
  supervisor: 'XXX',
  constructionLeader: 'XXX',
  completionTime: 'XXXXXXXXX',
  taskStatus: '图像待提交',
  creator: 'XXX',
  createTime: '2025-08-15 11:25:00',
})

const value = ref<string[]>([
  '项目信息',
])

// 获取机房类型标签样式
function getTypeTagType(type: string): TagType {
  return type === '汇聚机房' ? 'primary' : 'primary'
}

// 获取任务状态标签样式
function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case '待审核':
      return 'primary'
    case '已完成':
      return 'success'
    default:
      return 'warning'
  }
}

function handleEdit() {
  console.log('编辑操作')
}

function handleDelete() {
  console.log('删除操作')
}

function handleSpecification() {
  console.log('图像上传规范')
}
function handleSaveUpload() {
  console.log('保存')
  uploadItemRef.value?.onUploadClick()
}

function handleSubmitUpload() {
  console.log('提交上传')
}
</script>

<template>
  <view class="" p-t-16rpx pb-80px>
    <wd-collapse v-model="value" m-x-16rpx>
      <CollapsePanelItem title="项目信息" name="项目信息">
        <template v-if="1" #header-tools>
          <view flex gap-10px>
            <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
              编辑
            </wd-button>
            <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
              删除
            </wd-button>
          </view>
        </template>
        <!-- content -->
        <view>
          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>机房名称：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.name }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>编号：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.code }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>机房地址：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.address }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>机房类型：</text>
            <wd-tag :type="getTypeTagType(projectDetail.type)" size="small">
              {{ projectDetail.type }}
            </wd-tag>
            <text ml-20px mr-8px whitespace-nowrap text-14px color-gray-600>项目阶段：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.stage }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>框架名称：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.frameName }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>执行地市：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.executionCity }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>施工单位：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.constructionUnit }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>区公司机房建设负责人：</text>
            <text mr-8px break-all text-14px color-gray-900>{{ projectDetail.districtResponsible }}</text>
            <text ml-20px mr-8px whitespace-nowrap text-14px color-gray-600>地市机房建设负责人：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.cityResponsible }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>监理单位：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.supervisionUnit }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>监理员：</text>
            <text mr-8px break-all text-14px color-gray-900>{{ projectDetail.supervisor }}</text>
            <text ml-20px mr-8px whitespace-nowrap text-14px color-gray-600>施工队长：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.constructionLeader }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>收集完成时限：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.completionTime }}</text>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>任务状态：</text>
            <wd-tag :type="getStatusTagType(projectDetail.taskStatus)" size="small">
              {{ projectDetail.taskStatus }}
            </wd-tag>
          </view>

          <view flex="~ items-center wrap" mb-12px>
            <text mr-8px whitespace-nowrap text-14px color-gray-600>创建人：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.creator }}</text>
          </view>

          <view flex="~ items-center wrap">
            <text mr-8px whitespace-nowrap text-14px color-gray-600>创建时间：</text>
            <text flex-1 break-all text-14px color-gray-900>{{ projectDetail.createTime }}</text>
          </view>
        </view>
      </CollapsePanelItem>

      <CollapsePanelItem title="图片/视频上传" name="图片/视频上传">
        <template v-if="1" #header-tools>
          <!-- <wd-button type="info" :round="false" plain size="small" @click.stop="handleSpecification">
            图像上传规范
          </wd-button> -->
        </template>
        <view>
          <UploadItem ref="uploadItemRef" />
        </view>
      </CollapsePanelItem>
    </wd-collapse>

    <view shadow="0 -2px 8px rgba(0,0,0,0.1)" fixed bottom-0 left-0 right-0 z-100 bg-white p-16px>
      <!-- 保存 -->
      <view v-if="1">
        <view flex="~" gap-20rpx>
          <wd-button type="success" :round="false" plain block flex-1 @click="handleSaveUpload">
            保存
          </wd-button>
          <wd-button type="success" :round="false" block flex-1 @click="handleSubmitUpload">
            保存并提交
          </wd-button>
        </view>
      </view>
      <!-- 审核 -->
      <!-- <view v-else-if="1">
        <view flex="~" gap-20rpx>
          <wd-button type="error" :round="false" block flex-1>
            驳回
          </wd-button>
          <wd-button type="success" :round="false" block flex-1>
            同意
          </wd-button>
        </view>
      </view> -->
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>

<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "新增项目"

  }
}
</route>

<script lang="ts" setup>
const value = ref<string[]>(['项目信息新增'])
const formData = ref({
  value1: '',
  platform: '',
  value: '',
})
const platformList = ref([
  {
    label: '润建',
    value: '润建',
  },
  {
    label: '润建2',
    value: '润建2',
  },
])
async function handleSubmit() {
  // const valid = await formData.value.validate()
  // if (valid) {
  uni.showToast({
    title: '提交成功',
    icon: 'success',
  })
  // }
}
</script>

<template>
  <view pb="150rpx" min-h-screen class="global-bg-gray">
    <wd-collapse v-model="value" mx-16rpx>
      <wd-collapse-item title="项目信息新增" name="项目信息新增">
        <wd-form ref="form2" :model="formData">
          <wd-cell-group>
            <view mt-16rpx>
              <text text-sm>机房名称：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>编号：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>机房地址：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>机房类型：</text>
              <wd-select-picker
                v-model="formData.platform"
                prop="platform"
                :columns="platformList"
                placeholder="请选择内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>框架名称：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>机房所属地市公司：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx flex justify-between gap-14rpx>
              <view flex="1">
                <text text-sm>区公司机房建设负责人：</text>
                <wd-select-picker
                  v-model="formData.platform"
                  prop="platform"
                  :columns="platformList"
                  placeholder="请选择内容"
                />
              </view>
              <view flex="1" ml="20rpx">
                <text text-sm>地市机房建设负责人：</text>
                <wd-select-picker
                  v-model="formData.platform"
                  prop="platform"
                  :columns="platformList"
                  placeholder="请选择内容"
                />
              </view>
            </view>
            <view mt-16rpx>
              <text text-sm>监理单位：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>监理员：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>施工单位：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>施工队长：</text>
              <wd-input
                v-model="formData.value1"
                prop="value1"
                placeholder="请输入内容"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>收集完成期限：</text>
              <wd-datetime-picker v-model="formData.value" placeholder="请选择时间" type="datetime" use-second />
            </view>
          </wd-cell-group>
          <view class="footer">
            <view flex justify-around>
              <wd-button type="info" size="large" block @click="handleSubmit">
                取消
              </wd-button>
              <wd-button type="success" size="large" block @click="handleSubmit">
                保存
              </wd-button>
            </view>
          </view>
        </wd-form>
      </wd-collapse-item>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
.header-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}
:deep(.wd-input.is-cell),
:deep(.wd-cell),
:deep(.wd-cell__wrapper) {
  padding: 6rpx 0;
}
:deep(.wd-collapse-item__title::after) {
  content: '';
  position: absolute;
  top: 22rpx;
  left: 0;
  width: 4rpx;
  height: 50rpx;
  background-color: #0085d0;
}

:deep(.wd-collapse-item__header.is-expanded),
:deep(.wd-collapse-item__header) {
  padding: 0;
  padding: 20rpx;
  border-bottom: 4rpx solid #0085d080;
}
:deep(.wd-collapse-item__body) {
  padding: 10rpx;
}
:deep(.wd-input__value),
:deep(.wd-cell__wrapper) {
  border: 2rpx solid #c8cbcc;
  padding: 10rpx;
  max-height: 46rpx;
  min-height: 46rpx;
  border-radius: 10rpx;
}
:deep(.wd-input::after) {
  height: 0;
}
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 20rpx 0;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  .wd-button {
    width: 45%;
    border-radius: 10rpx;
  }
}
</style>

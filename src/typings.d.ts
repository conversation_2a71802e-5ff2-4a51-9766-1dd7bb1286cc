// 全局要用的类型放到这里

declare global {
  interface IResData<T> {
    code: number
    msg: string
    data?: T
    result?: T
  }

  // uni.uploadFile文件上传参数
  interface IUniUploadFileOptions {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  interface IUserInfo {
    nickname?: string
    avatar?: string
    /** 微信的 openid，非微信没有这个字段 */
    openid?: string
    token?: string
  }

  /** 项目状态类型 */
  type ProjectStatus = '图像待提交' | 'AI审核待确认' | 'AI识别待确认' | '申诉待审批' | '申诉已驳回' | '申诉已通过' | '已结束'
}

export {} // 防止模块污染
